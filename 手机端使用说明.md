# Android手机端游戏自动化脚本使用说明

## 📱 脚本文件说明

### 1. `auto_game.sh` - 配置文件版本脚本 (推荐)
- **功能**: 从配置文件读取命令序列并执行
- **用途**: 预设多个命令序列，按需执行
- **灵活性**: 高，支持多个预设序列，无需手动输入

### 2. `config_commands.txt` - 命令配置文件
- **功能**: 存储预设的命令序列
- **用途**: 配置各种游戏操作序列
- **特点**: 支持注释，易于管理和修改

### 3. `unified_commands.sh` - 通用统一命令脚本
- **功能**: 支持移动、点击、滑动的混合操作
- **用途**: 可以执行任意命令序列
- **灵活性**: 高，支持自定义命令组合

### 4. `game_sequence.sh` - 专用游戏序列脚本
- **功能**: 执行您提供的特定操作序列
- **用途**: 一键执行固定的游戏操作流程
- **灵活性**: 低，但执行效率高

## 🔧 环境要求

### 必需条件
1. **Android设备**: Android 4.0+ 
2. **Root权限**: 需要root权限才能执行shell脚本
3. **终端应用**: 推荐使用 Termux 或其他终端应用

### 推荐工具
- **Termux**: 免费的Android终端应用
- **Terminal Emulator**: 轻量级终端
- **ADB Shell**: 如果通过ADB连接

## 📥 部署步骤

### 方法一: 使用Termux (推荐)

1. **安装Termux**
   ```bash
   # 从Google Play或F-Droid下载安装Termux
   ```

2. **获取Root权限**
   ```bash
   # 在Termux中安装tsu (需要设备已root)
   pkg install tsu
   ```

3. **上传脚本文件**
   ```bash
   # 将脚本文件复制到手机存储
   # 可以通过USB、云盘、或直接在Termux中创建
   ```

4. **设置执行权限**
   ```bash
   chmod +x unified_commands.sh
   chmod +x game_sequence.sh
   ```

### 方法二: 直接在设备上创建

1. **打开终端应用**
2. **切换到root用户**
   ```bash
   su
   ```
3. **创建脚本文件**
   ```bash
   # 复制脚本内容到文件中
   cat > unified_commands.sh << 'EOF'
   # (粘贴脚本内容)
   EOF
   ```

## 🚀 使用方法

### 使用配置文件版本脚本 (auto_game.sh) - 推荐

```bash
# 显示配置文件内容
sh auto_game.sh -c

# 执行配置文件第1行命令 (默认)
sh auto_game.sh

# 执行配置文件第2行命令
sh auto_game.sh 2

# 显示帮助信息
sh auto_game.sh -h
```

### 配置文件管理 (config_commands.txt)

```bash
# 编辑配置文件添加新的命令序列
vi config_commands.txt

# 或使用其他编辑器
nano config_commands.txt
```

### 使用通用脚本 (unified_commands.sh)

```bash
# 基本语法
sh unified_commands.sh "命令序列"

# 示例1: 简单移动和点击
sh unified_commands.sh "W3 540,960 A2"

# 示例2: 您的具体操作序列
sh unified_commands.sh "2160,980 3000ms D3 1990,600 SWIPE:2000,500,1980,510,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 5000ms A4 5000ms A4 5000ms A4 5000ms A3 5000ms"

# 示例3: 复杂混合操作
sh unified_commands.sh "W2 1000ms 540,960 500ms SWIPE:800,600,800,300,400 D1"
```

### 使用专用脚本 (game_sequence.sh)

```bash
# 直接执行，无需参数
sh game_sequence.sh
```

## 📋 命令语法说明

### 移动命令
- `W3` - 向上移动3次
- `A2` - 向左移动2次  
- `S1` - 向下移动1次
- `D4` - 向右移动4次

### 点击命令
- `540,960` - 点击坐标(540, 960)
- `100,200` - 点击坐标(100, 200)

### 滑动命令
- `SWIPE:x1,y1,x2,y2,duration`
- `SWIPE:800,500,800,300,500` - 从(800,500)滑动到(800,300)，持续500ms

### 时间间隔
- `500ms` - 等待500毫秒
- `1000ms` - 等待1000毫秒(1秒)
- `3000ms` - 等待3000毫秒(3秒)

## 📝 配置文件说明

### 配置文件格式 (config_commands.txt)

```bash
# 这是注释行，会被忽略
# 空行也会被自动跳过

# 第1行命令: 您的游戏序列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1980,510,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 5000ms A4 5000ms A4 5000ms A4 5000ms A3 5000ms

# 第2行命令: 简单测试序列
W2 A2 S2 D2

# 第3行命令: 点击测试
540,960 1000ms 1080,540 1000ms 540,1920
```

### 配置文件规则

1. **注释行**: 以 `#` 开头的行会被忽略
2. **空行**: 空行会被自动跳过
3. **命令行**: 每行一个完整的命令序列
4. **行号**: 只计算有效的命令行，不包括注释和空行

### 添加新命令序列

```bash
# 编辑配置文件
vi config_commands.txt

# 在文件末尾添加新的命令序列
echo "W5 A3 540,960 D2" >> config_commands.txt

# 查看当前配置
sh auto_game.sh -c
```

## ⚠️ 注意事项

### 权限要求
- **必须有root权限**才能执行input命令
- 确保终端应用有root访问权限

### 游戏兼容性
- 脚本使用Android标准input命令
- 兼容大部分Android游戏
- 某些游戏可能有反作弊检测

### 坐标适配
- 脚本中的坐标基于特定屏幕分辨率
- 不同设备可能需要调整坐标值
- 建议先测试单个操作确认坐标准确性

## 🔍 故障排除

### 常见问题

1. **权限被拒绝**
   ```bash
   # 解决方案: 确保有root权限
   su
   # 然后再执行脚本
   ```

2. **命令不存在**
   ```bash
   # 检查input命令是否可用
   which input
   # 或者
   input --help
   ```

3. **脚本无法执行**
   ```bash
   # 检查文件权限
   ls -l *.sh
   # 设置执行权限
   chmod +x *.sh
   ```

4. **游戏无响应**
   - 确认游戏处于前台运行状态
   - 检查坐标是否正确
   - 验证按键映射是否有效

### 调试技巧

1. **测试单个命令**
   ```bash
   # 测试点击
   input tap 540 960
   
   # 测试按键
   input keyevent --longpress 51
   
   # 测试滑动
   input swipe 800 500 800 300 500
   ```

2. **查看日志输出**
   - 脚本会输出详细的执行日志
   - 观察每个步骤的执行情况

3. **分段测试**
   - 将复杂序列分解为小段测试
   - 逐步验证每个操作的效果

## 📱 推荐终端应用

### Termux (推荐)
- **优点**: 功能强大，支持包管理
- **下载**: Google Play Store 或 F-Droid
- **特点**: 无需root即可使用基本功能

### Terminal Emulator
- **优点**: 轻量级，启动快速
- **适用**: 简单脚本执行
- **特点**: 占用空间小

### ADB Shell
- **优点**: 通过USB连接使用
- **适用**: 开发调试环境
- **特点**: 无需在手机上安装额外应用

## 🎮 游戏使用建议

1. **首次使用前**
   - 先在测试环境验证脚本效果
   - 确认所有坐标和操作的准确性

2. **使用过程中**
   - 保持游戏处于前台状态
   - 避免在脚本执行时手动操作

3. **安全使用**
   - 遵守游戏服务条款
   - 合理控制自动化频率
   - 定期备份游戏数据

## 📞 技术支持

如果遇到问题，请检查：
1. 设备是否已正确root
2. 终端应用是否有root权限
3. 脚本文件是否有执行权限
4. 游戏是否处于可操作状态
5. 坐标是否适配当前设备分辨率
