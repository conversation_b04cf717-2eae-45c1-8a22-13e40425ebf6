#!/usr/bin/env python3
"""
测试间隔时间功能的脚本
验证统一命令执行中的步骤间隔时间参数
"""
import time
import subprocess
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# 键位映射
KEYMAP = {
    "W": "51",  # 向上
    "A": "29",  # 向左  
    "S": "47",  # 向下
    "D": "32"   # 向右
}

def execute_adb_command(command):
    """执行ADB命令"""
    try:
        result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout.strip()
    except Exception as e:
        return False, str(e)

def press_key_longpress(keycode, times, delay=0.3):
    """执行longpress按键"""
    for i in range(times):
        command = f"adb shell input keyevent --longpress {keycode}"
        success, _ = execute_adb_command(command)
        if not success:
            return False
        if i < times - 1:
            time.sleep(delay)
    return True

def tap_screen(x, y):
    """屏幕点击"""
    command = f"adb shell input tap {x} {y}"
    success, _ = execute_adb_command(command)
    return success

def swipe_screen(x1, y1, x2, y2, duration=500):
    """屏幕滑动"""
    command = f"adb shell input swipe {x1} {y1} {x2} {y2} {duration}"
    success, _ = execute_adb_command(command)
    return success

def test_delay_functionality():
    """测试间隔时间功能"""
    print("=== 间隔时间功能测试 ===")
    
    # 测试用例
    test_cases = [
        # 基本间隔时间测试
        ("W2 1000ms A2", "移动W2次 → 等待1000ms → 移动A2次"),
        
        # 多个间隔时间
        ("540,960 500ms D3 1500ms 100,200", "点击中心 → 等待500ms → 移动D3次 → 等待1500ms → 点击左上角"),
        
        # 混合命令与间隔时间
        ("W1 800ms SWIPE:800,500,800,300,400 2000ms 540,960", 
         "移动W1次 → 等待800ms → 滑动 → 等待2000ms → 点击中心"),
        
        # 复杂的间隔时间序列
        ("2160,980 500ms D3 500ms 1990,600 1000ms 2000,860", 
         "点击(2160,980) → 等待500ms → D键3次 → 等待500ms → 点击(1990,600) → 等待1000ms → 点击(2000,860)"),
        
        # 不同长度的间隔时间
        ("A1 100ms S1 2000ms W1 5000ms D1", 
         "A1次 → 等待100ms → S1次 → 等待2000ms → W1次 → 等待5000ms → D1次"),
    ]
    
    for i, (command, description) in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {description} ---")
        print(f"命令: {command}")
        
        # 解析命令以显示预期的执行计划
        commands = command.split()
        action_plan = []
        
        for cmd in commands:
            cmd = cmd.strip()
            if not cmd:
                continue
            
            # 间隔时间参数
            if cmd.lower().endswith('ms'):
                try:
                    delay_value = int(cmd[:-2])
                    if action_plan:
                        action_plan[-1]['delay_after'] = delay_value / 1000.0
                    continue
                except ValueError:
                    continue
            
            # 滑动命令
            elif cmd.upper().startswith('SWIPE:'):
                swipe_params = cmd[6:]
                try:
                    x1, y1, x2, y2, duration = map(int, swipe_params.split(','))
                    action_plan.append({
                        'type': 'swipe',
                        'params': (x1, y1, x2, y2, duration),
                        'display': f"滑动({x1},{y1})→({x2},{y2})",
                        'delay_after': 0.3
                    })
                except:
                    continue
            
            # 点击命令
            elif ',' in cmd:
                try:
                    x, y = map(int, cmd.split(','))
                    action_plan.append({
                        'type': 'tap',
                        'params': (x, y),
                        'display': f"点击({x},{y})",
                        'delay_after': 0.3
                    })
                except:
                    continue
            
            # 移动命令
            else:
                try:
                    direction = cmd[0].upper()
                    count = int(cmd[1:])
                    if direction in KEYMAP:
                        action_plan.append({
                            'type': 'move',
                            'params': (KEYMAP[direction], count),
                            'display': f"移动{direction}×{count}",
                            'delay_after': 0.3
                        })
                except:
                    continue
        
        # 显示执行计划
        print("执行计划:")
        for j, action in enumerate(action_plan):
            delay_info = ""
            if j < len(action_plan) - 1:  # 不是最后一个
                delay = action.get('delay_after', 0.3)
                if delay != 0.3:
                    delay_info = f" → 等待{delay*1000:.0f}ms"
                else:
                    delay_info = f" → 等待{delay*1000:.0f}ms(默认)"
            print(f"  {j+1}. {action['display']}{delay_info}")
        
        input("\n按回车开始执行...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行命令序列
        for j, action in enumerate(action_plan):
            step_start = time.time()
            print(f"执行: {action['display']}", end=" ")
            
            if action['type'] == 'move':
                keycode, count = action['params']
                success = press_key_longpress(keycode, count)
            elif action['type'] == 'tap':
                x, y = action['params']
                success = tap_screen(x, y)
            elif action['type'] == 'swipe':
                x1, y1, x2, y2, duration = action['params']
                success = swipe_screen(x1, y1, x2, y2, duration)
            else:
                success = False
            
            step_end = time.time()
            print(f"✓ ({step_end - step_start:.2f}s)" if success else "❌")
            
            # 步骤间等待
            if j < len(action_plan) - 1:
                delay_time = action.get('delay_after', 0.3)
                if delay_time != 0.3:
                    print(f"等待 {delay_time*1000:.0f}ms...")
                time.sleep(delay_time)
        
        total_time = time.time() - start_time
        print(f"✓ 测试完成！总耗时: {total_time:.2f}秒\n")

if __name__ == "__main__":
    # 检查ADB连接
    print("检查ADB连接...")
    success, output = execute_adb_command("adb devices")
    if not success or "device" not in output:
        print("❌ ADB连接失败")
        exit(1)
    
    print("✓ ADB连接正常")
    test_delay_functionality()
