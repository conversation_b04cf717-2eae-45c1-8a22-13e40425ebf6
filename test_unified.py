#!/usr/bin/env python3
"""
统一命令功能测试脚本
测试移动、点击、滑动的混合操作
"""
import time
import subprocess
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# 键位映射
KEYMAP = {
    "W": "51",  # 向上
    "A": "29",  # 向左  
    "S": "47",  # 向下
    "D": "32"   # 向右
}

def execute_adb_command(command):
    """执行ADB命令"""
    try:
        result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout.strip()
    except Exception as e:
        return False, str(e)

def press_key_longpress(keycode, times, delay=0.3):
    """执行longpress按键"""
    for i in range(times):
        command = f"adb shell input keyevent --longpress {keycode}"
        success, _ = execute_adb_command(command)
        if not success:
            return False
        if i < times - 1:
            time.sleep(delay)
    return True

def tap_screen(x, y):
    """屏幕点击"""
    command = f"adb shell input tap {x} {y}"
    success, _ = execute_adb_command(command)
    return success

def swipe_screen(x1, y1, x2, y2, duration=500):
    """屏幕滑动"""
    command = f"adb shell input swipe {x1} {y1} {x2} {y2} {duration}"
    success, _ = execute_adb_command(command)
    return success

def get_screen_resolution():
    """获取屏幕分辨率"""
    command = "adb shell wm size"
    success, output = execute_adb_command(command)
    if success:
        try:
            size_part = output.split(':')[-1].strip()
            width, height = map(int, size_part.split('x'))
            return width, height
        except:
            return None, None
    return None, None

def test_unified_commands():
    """测试统一命令功能"""
    print("=== 统一命令功能测试 ===")
    
    # 获取屏幕分辨率
    width, height = get_screen_resolution()
    if width and height:
        print(f"屏幕分辨率: {width}x{height}")
        center_x, center_y = width // 2, height // 2
        right_x = int(width * 0.8)
    else:
        print("无法获取屏幕分辨率，使用默认值")
        center_x, center_y = 540, 960
        right_x = 800
    
    # 测试用例
    test_cases = [
        # 基本移动
        ("W3", "向上移动3次"),
        
        # 基本点击
        (f"{center_x},{center_y}", "点击屏幕中心"),
        
        # 基本滑动 (右侧区域向上滑动，用于视角转动)
        (f"SWIPE:{right_x},600,{right_x},300,500", "右侧向上滑动(视角向上)"),
        
        # 混合操作1: 移动 + 点击
        (f"W2 {center_x},{center_y}", "向上移动2次然后点击中心"),
        
        # 混合操作2: 移动 + 滑动
        (f"A3 SWIPE:{right_x},400,{right_x},600,300", "向左移动3次然后右侧向下滑动"),
        
        # 复杂混合: 移动 + 点击 + 滑动
        (f"W1 {center_x},{center_y} SWIPE:{right_x},500,{right_x},300,400 D2", 
         "向上1次→点击中心→右侧向上滑动→向右2次"),
        
        # 多次滑动 (模拟视角调整)
        (f"SWIPE:{right_x},500,{right_x},300,300 SWIPE:{right_x},300,{right_x},500,300", 
         "连续滑动调整视角"),
    ]
    
    for i, (command, description) in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {description} ---")
        print(f"命令: {command}")
        input("按回车执行...")
        
        # 解析并执行命令
        commands = command.split()
        for cmd in commands:
            cmd = cmd.strip()
            
            # 滑动命令
            if cmd.upper().startswith('SWIPE:'):
                swipe_params = cmd[6:]
                try:
                    x1, y1, x2, y2, duration = map(int, swipe_params.split(','))
                    print(f"执行滑动: ({x1},{y1})→({x2},{y2}), {duration}ms", end=" ")
                    success = swipe_screen(x1, y1, x2, y2, duration)
                    print("✓" if success else "❌")
                except Exception as e:
                    print(f"滑动命令错误: {e}")
            
            # 点击命令
            elif ',' in cmd:
                try:
                    x, y = map(int, cmd.split(','))
                    print(f"执行点击: ({x},{y})", end=" ")
                    success = tap_screen(x, y)
                    print("✓" if success else "❌")
                except Exception as e:
                    print(f"点击命令错误: {e}")
            
            # 移动命令
            else:
                try:
                    direction = cmd[0].upper()
                    count = int(cmd[1:])
                    if direction in KEYMAP:
                        print(f"执行移动: {direction}×{count}", end=" ")
                        success = press_key_longpress(KEYMAP[direction], count)
                        print("✓" if success else "❌")
                except Exception as e:
                    print(f"移动命令错误: {e}")
            
            time.sleep(0.3)  # 命令间间隔
        
        print("测试完成！")

if __name__ == "__main__":
    # 检查ADB连接
    print("检查ADB连接...")
    success, output = execute_adb_command("adb devices")
    if not success or "device" not in output:
        print("❌ ADB连接失败")
        exit(1)
    
    print("✓ ADB连接正常")
    test_unified_commands()
