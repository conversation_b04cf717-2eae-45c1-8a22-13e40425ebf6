# 元梦之星农场自动化脚本使用说明

## 配置参数

脚本开头有几个重要参数，可以根据需要调整：

```bash
# 时间间隔参数 (单位: 秒)
DEFAULT_INTERVAL=0.8    # 命令之间的默认间隔 (800ms)
KEY_INTERVAL=0.8        # 按键之间的间隔 (800ms)
SEQ_INTERVAL=2.0        # 命令序列之间的间隔 (2000ms)
```

## 配置文件格式

配置文件 `config_commands.txt` 的格式如下：

1. 每行一个命令序列，清晰易维护
2. 以`#`开头的行会被视为注释
3. 空行会被自动忽略

例如：
```
# 农场第六列
2160,980 3000ms D3 1990,600 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms

# 农场第五列
2160,980 3000ms D3 1990,600 SWIPE:2000,500,1980,510,500 2000,860 2000ms SWIPE:2000,500,2070,500,500 3000ms A4 3000ms A4 3000ms A4 3000ms A3 3000ms
```

## 支持的命令

1. **点击命令**：`x,y` - 例如 `540,960`
2. **滑动命令**：`SWIPE:x1,y1,x2,y2,duration` - 例如 `SWIPE:800,500,800,300,500`
3. **移动命令**：`[WASD]数字` - 例如 `W2`, `A3`, `S1`, `D4`
4. **等待命令**：`数字ms` - 例如 `1000ms`, `3000ms`

## 使用方法

```bash
# 查看配置
sh auto_game.sh -c

# 执行所有命令行（默认行为）
sh auto_game.sh

# 执行农场第六列
sh auto_game.sh 1

# 执行农场第五列
sh auto_game.sh 2

# 执行农场第四列
sh auto_game.sh 3

# 执行农场第三列
sh auto_game.sh 4

# 执行农场第二列
sh auto_game.sh 5

# 执行农场第一列
sh auto_game.sh 6

# 执行简单移动测试
sh auto_game.sh 7

# 执行点击测试
sh auto_game.sh 8

# 执行滑动测试
sh auto_game.sh 9

# 显示帮助
sh auto_game.sh -h
```

## 执行流程

### 执行所有命令行（默认）

当执行 `sh auto_game.sh` 时：

1. 读取配置文件所有有效命令行（跳过注释和空行）
2. 依次执行每一行：
   - 执行农场第六列 → 等待2秒
   - 执行农场第五列 → 等待2秒
   - 执行农场第四列 → 等待2秒
   - 执行农场第三列 → 等待2秒
   - 执行农场第二列 → 等待2秒
   - 执行农场第一列 → 等待2秒
   - 执行简单移动测试 → 等待2秒
   - 执行点击测试 → 等待2秒
   - 执行滑动测试 → 完成

### 执行单行命令

当执行 `sh auto_game.sh 1` 时：

1. 读取配置文件第1行有效命令（跳过注释和空行）
2. 解析该行的所有命令
3. 依次执行每个命令：
   - 点击 (2160,980)
   - 等待 3000ms
   - 向右移动 3 次
   - 点击 (1990,600)
   - ...以此类推

## 当前配置文件包含

- 第1行：农场第六列
- 第2行：农场第五列
- 第3行：农场第四列
- 第4行：农场第三列
- 第5行：农场第二列
- 第6行：农场第一列
- 第7行：简单移动测试
- 第8行：点击测试
- 第9行：滑动测试

## 注意事项

1. 确保每个命令格式正确，否则会导致执行失败
2. 命令之间用空格分隔
3. 序列之间用分号分隔
4. 如果执行失败，请检查日志输出，找出错误原因
