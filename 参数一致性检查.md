# 参数一致性检查报告

## 📋 检查结果

### ✅ 已确保一致的参数

#### 1. 键位映射
**auto_game.sh** 和 **move_debugger.py** 中的键位映射完全一致：

```
W键: keycode=51  # 向上移动
A键: keycode=29  # 向左移动  
S键: keycode=47  # 向下移动
D键: keycode=32  # 向右移动
```

#### 2. 时间间隔参数
现在两个文件中的时间间隔参数已统一：

**auto_game.sh**:
```bash
DEFAULT_INTERVAL=0.8    # 命令之间的默认间隔 (800ms)
KEY_INTERVAL=0.8        # 按键之间的间隔 (800ms)
SEQ_INTERVAL=2.0        # 命令序列之间的间隔 (2000ms)
```

**move_debugger.py**:
```python
DEFAULT_INTERVAL = 0.8    # 命令之间的默认间隔 (800ms)
KEY_INTERVAL = 0.8        # 按键之间的间隔 (800ms)
SEQ_INTERVAL = 2.0        # 命令序列之间的间隔 (2000ms)
```

#### 3. 按键执行方法
两个文件都使用相同的按键执行方法：

**auto_game.sh**:
```bash
input keyevent --longpress $keycode
```

**move_debugger.py**:
```python
adb shell input keyevent --longpress {keycode}
```

## 🔧 修改内容

### move_debugger.py 中的修改

1. **添加配置参数区域**：
   - 在文件开头添加了与 auto_game.sh 一致的时间间隔参数

2. **函数默认参数修改**：
   - `press_key_optimized()`: delay 参数从 0.5 改为 KEY_INTERVAL
   - `press_key()`: delay 参数从 0.5 改为 KEY_INTERVAL
   - 统一命令执行中的 delay_after 从 0.5 改为 DEFAULT_INTERVAL

3. **测试函数参数修改**：
   - 校准功能中的间隔时间使用 KEY_INTERVAL
   - 单次按键测试使用 KEY_INTERVAL
   - 用户输入提示显示正确的默认值

## 📊 参数使用场景

### DEFAULT_INTERVAL (0.8秒)
- **用途**: 不同命令之间的间隔
- **使用场景**: 
  - 点击命令后的等待
  - 滑动命令后的等待
  - 移动命令后的等待

### KEY_INTERVAL (0.8秒)  
- **用途**: 同一移动命令中多次按键之间的间隔
- **使用场景**:
  - W3 命令中，3次W键按压之间的间隔
  - A2 命令中，2次A键按压之间的间隔

### SEQ_INTERVAL (2.0秒)
- **用途**: 不同命令序列之间的间隔
- **使用场景**:
  - auto_game.sh 中执行完一行命令后，开始下一行命令前的等待
  - 给用户足够时间观察游戏状态变化

## ✅ 验证方法

可以通过以下方式验证参数一致性：

1. **运行 move_debugger.py**:
   ```bash
   python move_debugger.py
   ```
   选择移动测试，观察间隔时间是否为0.8秒

2. **运行 auto_game.sh**:
   ```bash
   sh auto_game.sh 7  # 执行简单移动测试
   ```
   观察移动命令的执行间隔

3. **对比日志输出**:
   两个程序的日志应该显示相同的时间间隔值

## 📝 注意事项

1. **参数修改**: 如需调整时间间隔，请同时修改两个文件中的对应参数
2. **测试验证**: 修改参数后，建议先用 move_debugger.py 测试效果，再用 auto_game.sh 执行实际任务
3. **备份配置**: 重要参数修改前建议备份原始配置

现在两个文件的参数已完全一致，确保了行为的统一性！
