# Android农场游戏自动化脚本

## 项目简介
这是一个专为Android手机农场游戏设计的自动化脚本，使用ADB命令模拟WASD按键操作，实现精确的角色移动控制。

## 核心特性
- ✅ 使用 `adb shell input keyevent --longpress` 实现稳定的按键控制
- ✅ 详细的日志记录和错误诊断
- ✅ 移动距离校准系统
- ✅ 复杂移动序列执行
- ✅ 实时ADB连接监控

## 环境要求
1. **ADB工具**: 确保ADB已安装并添加到系统PATH
2. **Python 3.6+**: 运行脚本需要Python环境
3. **Android设备**: 开启USB调试模式并连接到电脑

## 快速开始

### 1. 检查ADB连接
```bash
adb devices
```
确保显示你的设备状态为 `device`

### 2. 运行主脚本
```bash
python move_debugger.py
```

### 3. 功能说明

#### 主要功能菜单：
1. **移动测试** - 测试单个方向的移动
2. **移动距离校准** - 确定按键次数与移动距离的关系
3. **统一命令执行** - 融合移动/点击/滑动的混合操作 ⭐
4. **单次按键测试** - 验证按键是否有效
5. **ADB连接状态** - 检查设备连接
6. **屏幕信息** - 获取设备屏幕参数

## 使用流程

### 第一步：验证按键有效性
选择菜单 `5. 单次按键测试`，确认每个方向键都能让角色移动。

### 第二步：校准移动距离
选择菜单 `2. 移动距离校准`：
- 选择要校准的方向（W/A/S/D）
- 系统会测试按1次、2次、3次、5次、10次的移动效果
- 记录每次测试的移动距离
- 校准数据会保存到 `movement_calibration.txt`

### 第三步：执行统一命令
选择菜单 `3. 统一命令执行`：
- 支持移动、点击、滑动的混合操作
- 移动命令：`W3 A2 S1 D4`
- 点击命令：`540,960` (x,y坐标)
- 滑动命令：`SWIPE:800,500,800,300,500` (起点x,y,终点x,y,持续时间ms)
- 混合命令：`W3 540,960 A2 SWIPE:800,500,800,300,500`

## 统一命令语法

### 移动命令
```
方向键 + 次数
```
- `W5` - 向上移动5次
- `A3 D2` - 向左3次，然后向右2次
- 方向键：W(上) A(左) S(下) D(右)

### 点击命令
```
x,y坐标
```
- `540,960` - 点击坐标(540, 960)
- `100,200 800,600` - 连续点击两个位置

### 滑动命令
```
SWIPE:x1,y1,x2,y2,duration
```
- `SWIPE:800,500,800,300,500` - 从(800,500)滑动到(800,300)，持续500ms
- 主要用于游戏视角转动，推荐使用屏幕右侧区域
- duration影响滑动速度：值越小滑动越快

### 间隔时间参数
```
数字 + ms后缀
```
- `500ms` - 等待500毫秒
- `1000ms` - 等待1000毫秒（1秒）
- `2000ms` - 等待2000毫秒（2秒）
- 间隔时间参数应用到前一个命令之后
- 如果不指定，使用默认的300ms间隔

### 混合命令示例
- `W3 540,960` - 向上3次然后点击中心（默认间隔）
- `W3 1000ms 540,960` - 向上3次→等待1000ms→点击中心
- `A2 500ms SWIPE:800,600,800,300,400 D1` - 向左2次→等待500ms→滑动→向右1次
- `2160,980 500ms D3 500ms 1990,600 2000ms 2000,860` - 复杂的定时序列操作

## 文件说明

### 主要文件
- `move_debugger.py` - 主脚本，包含所有功能
- `quick_test.py` - 快速测试不同按键方法的脚本
- `README.md` - 使用说明文档

### 生成的文件
- `move_debugger.log` - 详细的操作日志
- `movement_calibration.txt` - 移动距离校准数据

## 技术原理

### 为什么选择keyevent而不是swipe？
1. **精确性**: keyevent是离散事件，移动距离更可控
2. **稳定性**: 不受滑动时间和速度影响
3. **重现性**: 相同的按键次数产生相同的移动效果

### 为什么使用--longpress？
经过测试发现，普通的keyevent无法让游戏角色移动，只有longpress方法才能被游戏识别为有效的按键输入。

## 故障排除

### 常见问题
1. **角色不移动**
   - 确认使用的是longpress方法
   - 检查游戏是否处于可移动状态
   - 验证按键映射是否正确

2. **ADB连接失败**
   - 检查USB调试是否开启
   - 确认ADB驱动是否正确安装
   - 尝试重新连接设备

3. **按键无响应**
   - 确认游戏窗口处于焦点状态
   - 检查设备是否锁屏
   - 验证keycode是否正确

### 调试技巧
- 查看 `move_debugger.log` 获取详细错误信息
- 使用单次按键测试验证每个方向
- 逐步增加移动次数，观察效果变化

## 扩展功能

### 自动化脚本开发
基于校准数据，可以开发更复杂的自动化功能：
- 自动收菜路径规划
- 定时任务执行
- 多点位巡逻
- 资源采集优化

### 虚拟手柄
未来可以开发图形界面，提供：
- 屏幕上的WASD控制按钮
- 实时移动距离显示
- 路径录制和回放功能

## 注意事项
- 请遵守游戏服务条款，合理使用自动化功能
- 建议在测试环境中先验证脚本效果
- 定期备份校准数据和重要配置
