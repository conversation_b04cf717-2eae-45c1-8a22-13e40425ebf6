#!/system/bin/sh
# Android农场游戏自动化脚本 - 分号分隔版本

# ==================== 配置参数 ====================
CONFIG_FILE="config_commands.txt"

# 时间间隔参数 (单位: 秒)
DEFAULT_COMMAND_INTERVAL=0.8    # 命令之间的默认间隔 (800ms)
KEYPRESS_INTERVAL=0.8           # 按键之间的间隔 (800ms)
SEQUENCE_INTERVAL=2.0           # 命令序列之间的间隔 (2000ms)
FALLBACK_DELAY=1.0              # 时间解析失败时的备用延迟

# 调试模式 (1=开启, 0=关闭)
DEBUG_MODE=1

# ==================== 核心函数 ====================

# 日志函数
log() {
    if [ $DEBUG_MODE -eq 1 ]; then
        echo "[$(date '+%H:%M:%S')] $1"
    fi
}

# 检查是否为数字
is_num() {
    case "$1" in
        ''|*[!0-9]*) return 1 ;;
        *) return 0 ;;
    esac
}

# 执行单个命令
execute_command() {
    local cmd="$1"
    
    log "执行命令: $cmd"
    
    # 时间间隔命令 (xxxms)
    case "$cmd" in
        *ms)
            local delay=$(echo "$cmd" | sed 's/ms$//')
            if is_num "$delay"; then
                log "等待 ${delay}ms"
                sleep $(echo "scale=3; $delay / 1000" | bc 2>/dev/null || echo "$FALLBACK_DELAY")
                return 0
            else
                log "ERROR: 无效时间格式 $cmd"
                return 1
            fi
            ;;
    esac
    
    # 滑动命令 (SWIPE:x1,y1,x2,y2,duration)
    case "$cmd" in
        SWIPE:*)
            local params=$(echo "$cmd" | cut -d: -f2)
            local x1=$(echo "$params" | cut -d, -f1)
            local y1=$(echo "$params" | cut -d, -f2)
            local x2=$(echo "$params" | cut -d, -f3)
            local y2=$(echo "$params" | cut -d, -f4)
            local dur=$(echo "$params" | cut -d, -f5)
            
            if is_num "$x1" && is_num "$y1" && is_num "$x2" && is_num "$y2" && is_num "$dur"; then
                log "滑动: ($x1,$y1) → ($x2,$y2) ${dur}ms"
                input swipe $x1 $y1 $x2 $y2 $dur
                return $?
            else
                log "ERROR: 滑动参数错误 $cmd"
                return 1
            fi
            ;;
    esac
    
    # 点击命令 (x,y)
    case "$cmd" in
        *,*)
            local x=$(echo "$cmd" | cut -d, -f1)
            local y=$(echo "$cmd" | cut -d, -f2)
            
            if is_num "$x" && is_num "$y"; then
                log "点击: ($x,$y)"
                input tap $x $y
                return $?
            else
                log "ERROR: 点击坐标错误 $cmd"
                return 1
            fi
            ;;
    esac
    
    # 移动命令 (W3, A2, S1, D4)
    case "$cmd" in
        [WwAaSsDd][0-9]*)
            local dir=$(echo "$cmd" | cut -c1 | tr '[:lower:]' '[:upper:]')
            local num=$(echo "$cmd" | cut -c2-)
            
            if ! is_num "$num" || [ "$num" -le 0 ]; then
                log "ERROR: 移动次数错误 $cmd"
                return 1
            fi
            
            # 键位映射
            local keycode=""
            case "$dir" in
                W) keycode=51 ;;  # 上
                A) keycode=29 ;;  # 左
                S) keycode=47 ;;  # 下
                D) keycode=32 ;;  # 右
                *) 
                    log "ERROR: 无效方向 $dir"
                    return 1
                    ;;
            esac
            
            log "移动: $dir 方向 $num 次"
            local i=1
            while [ $i -le $num ]; do
                input keyevent --longpress $keycode
                if [ $i -lt $num ]; then
                    sleep $KEYPRESS_INTERVAL
                fi
                i=$((i + 1))
            done
            return 0
            ;;
    esac
    
    log "ERROR: 无法识别命令 $cmd"
    return 1
}

# 执行单个命令序列
run_single_sequence() {
    local sequence="$1"
    
    if [ -z "$sequence" ]; then
        log "WARNING: 命令序列为空，跳过"
        return 0
    fi
    
    log "开始执行命令序列: $sequence"
    
    # 计算非时间间隔命令的总数
    local cmd_count=0
    for cmd in $sequence; do
        case "$cmd" in
            *ms) ;;  # 时间间隔不计入命令数
            *) cmd_count=$((cmd_count + 1)) ;;
        esac
    done
    
    log "序列包含 $cmd_count 个命令"
    
    # 执行命令序列
    local current_cmd=0
    for cmd in $sequence; do
        # 执行命令
        if ! execute_command "$cmd"; then
            log "ERROR: 命令执行失败，停止当前序列"
            return 1
        fi
        
        # 如果不是时间间隔命令，增加计数并添加默认间隔
        case "$cmd" in
            *ms) ;;  # 时间间隔命令不需要额外间隔
            *)
                current_cmd=$((current_cmd + 1))
                # 如果不是最后一个命令，添加默认间隔
                if [ $current_cmd -lt $cmd_count ]; then
                    log "默认间隔 ${DEFAULT_COMMAND_INTERVAL}s"
                    sleep $DEFAULT_COMMAND_INTERVAL
                fi
                ;;
        esac
    done
    
    log "命令序列执行完成"
    return 0
}

# 执行多个命令序列（分号分隔）
run_multiple_sequences() {
    local sequences_line="$1"
    
    if [ -z "$sequences_line" ]; then
        log "ERROR: 命令行为空"
        return 1
    fi
    
    log "解析分号分隔的命令序列: $sequences_line"
    
    # 使用分号分割命令序列
    local sequence_count=0
    local old_IFS="$IFS"
    IFS=';'
    
    for sequence in $sequences_line; do
        sequence_count=$((sequence_count + 1))
        
        # 去除前后空格
        sequence=$(echo "$sequence" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        
        if [ -n "$sequence" ]; then
            log "=== 执行第 $sequence_count 个命令序列 ==="
            
            if ! run_single_sequence "$sequence"; then
                log "ERROR: 第 $sequence_count 个序列执行失败"
                IFS="$old_IFS"
                return 1
            fi
            
            # 序列间间隔（除了最后一个序列）
            # 检查是否还有更多序列
            local remaining=$(echo "$sequences_line" | cut -d';' -f$((sequence_count + 1))- 2>/dev/null)
            if [ -n "$remaining" ] && [ "$remaining" != "$sequences_line" ]; then
                log "序列间间隔 ${SEQUENCE_INTERVAL}s"
                sleep $SEQUENCE_INTERVAL
            fi
        fi
    done
    
    IFS="$old_IFS"
    log "所有命令序列执行完成！共执行 $sequence_count 个序列"
    return 0
}

# 读取配置文件
read_config() {
    local line_num="${1:-1}"
    
    if [ ! -f "$CONFIG_FILE" ]; then
        log "ERROR: 配置文件不存在 $CONFIG_FILE"
        return 1
    fi
    
    log "读取配置文件: $CONFIG_FILE, 行号: $line_num"
    
    local current=0
    local valid=0
    
    while IFS= read -r line || [ -n "$line" ]; do
        current=$((current + 1))
        
        # 跳过注释和空行
        case "$line" in
            \#*|'') continue ;;
        esac
        
        valid=$((valid + 1))
        
        if [ $valid -eq $line_num ]; then
            log "找到第 $line_num 行: $line"
            run_multiple_sequences "$line"
            return $?
        fi
    done < "$CONFIG_FILE"
    
    log "ERROR: 未找到第 $line_num 行命令 (共 $valid 行有效命令)"
    return 1
}

# 显示配置
show_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        log "ERROR: 配置文件不存在 $CONFIG_FILE"
        return 1
    fi
    
    echo "=== 配置参数 ==="
    echo "默认命令间隔: ${DEFAULT_COMMAND_INTERVAL}s"
    echo "按键间隔: ${KEYPRESS_INTERVAL}s"
    echo "序列间隔: ${SEQUENCE_INTERVAL}s"
    echo "调试模式: $DEBUG_MODE"
    echo ""
    
    echo "=== 配置文件内容 ($CONFIG_FILE) ==="
    local line_num=0
    local valid=0
    
    while IFS= read -r line || [ -n "$line" ]; do
        line_num=$((line_num + 1))
        
        case "$line" in
            \#*|'') 
                echo "    $line"
                continue 
                ;;
        esac
        
        valid=$((valid + 1))
        
        # 显示序列数量
        local seq_count=$(echo "$line" | tr -cd ';' | wc -c)
        seq_count=$((seq_count + 1))
        
        echo "[$valid] 包含 $seq_count 个命令序列"
        echo "    $line"
        echo ""
    done < "$CONFIG_FILE"
    
    echo "=== 共 $valid 行有效命令 ==="
}

# 显示使用说明
show_usage() {
    echo "Android农场游戏自动化脚本 - 分号分隔版本"
    echo ""
    echo "配置参数:"
    echo "  默认命令间隔: ${DEFAULT_COMMAND_INTERVAL}s"
    echo "  按键间隔: ${KEYPRESS_INTERVAL}s"
    echo "  序列间隔: ${SEQUENCE_INTERVAL}s"
    echo "  配置文件: $CONFIG_FILE"
    echo ""
    echo "使用方法:"
    echo "  sh game_script.sh [选项] [行号]"
    echo ""
    echo "选项:"
    echo "  -c, --config    显示配置文件内容"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "参数:"
    echo "  行号            执行配置文件中指定行的命令 (默认: 1)"
    echo ""
    echo "示例:"
    echo "  sh game_script.sh           # 执行第1行的所有序列"
    echo "  sh game_script.sh 2         # 执行第2行的所有序列"
    echo "  sh game_script.sh -c        # 显示配置文件内容"
}

# ==================== 主函数 ====================

main() {
    case "$1" in
        -c|--config)
            show_config
            ;;
        -h|--help)
            show_usage
            ;;
        '')
            read_config 1
            ;;
        *)
            if is_num "$1" && [ "$1" -gt 0 ]; then
                read_config "$1"
            else
                log "ERROR: 无效行号 $1"
                show_usage
                exit 1
            fi
            ;;
    esac
}

# 执行主函数
main "$@"
